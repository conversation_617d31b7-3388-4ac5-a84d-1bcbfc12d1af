import { useState, useEffect } from 'react';
import { adminService } from '../../services/endpoints';
import { ORDER_STATUS } from '../../utils/constants';

const AdminOrders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(''); // Clear any previous errors
      console.log('🔍 Admin: Fetching all orders...');

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 10000)
      );

      const response = await Promise.race([
        adminService.getOrders(),
        timeoutPromise
      ]);
      console.log('🔍 Admin: Orders response:', response);
      console.log('🔍 Admin: Response type:', typeof response);
      console.log('🔍 Admin: Response keys:', Object.keys(response || {}));

      // Log all properties to see the actual structure
      if (response && typeof response === 'object') {
        Object.keys(response).forEach(key => {
          console.log(`🔍 Admin: response.${key}:`, response[key]);
          console.log(`🔍 Admin: response.${key} type:`, typeof response[key]);
          if (Array.isArray(response[key])) {
            console.log(`🔍 Admin: response.${key} is array with length:`, response[key].length);
          }
        });
      }

      let ordersData = [];

      // Handle the exact backend response structure you showed
      if (Array.isArray(response)) {
        ordersData = response;
        console.log('🔍 Admin: Using direct response array');
      } else if (response?.data?.data && Array.isArray(response.data.data)) {
        // This is the structure from your backend: response.data.data
        ordersData = response.data.data;
        console.log('🔍 Admin: Using response.data.data array (backend structure)');
      } else if (response?.data && Array.isArray(response.data)) {
        // This is what you're seeing: { "data": [] }
        ordersData = response.data;
        console.log('🔍 Admin: Using response.data array (your current structure)');
      } else if (response?.results && Array.isArray(response.results)) {
        ordersData = response.results;
        console.log('🔍 Admin: Using response.results array');
      } else {
        ordersData = [];
        console.log('🔍 Admin: No array found, using empty array');
        console.log('🔍 Admin: Response structure not recognized:', response);
      }

      console.log('🔍 Admin: Final orders data:', ordersData);
      console.log('🔍 Admin: Orders count:', ordersData.length);

      setOrders(ordersData);

      // If no orders found, try alternative endpoint or show helpful message
      if (ordersData.length === 0) {
        console.log('🔍 Admin: No orders found. This could mean:');
        console.log('1. No orders have been placed yet');
        console.log('2. API endpoint might be different');
        console.log('3. User permissions might be insufficient');
      }
    } catch (error) {
      setError('Failed to fetch orders');
      console.error('🔍 Admin: Error fetching orders:', error);
      console.error('🔍 Admin: Error response:', error.response);
    } finally {
      setLoading(false);
    }
  };

  const handleOrderAction = async (orderId, action) => {
    try {
      setActionLoading(true);
      let response;
      
      switch (action) {
        case 'approve':
          response = await adminService.approveOrder(orderId);
          break;
        case 'reject':
          response = await adminService.rejectOrder(orderId);
          break;
        case 'delivered':
          response = await adminService.markOrderDelivered(orderId);
          break;
        case 'delete':
          response = await adminService.deleteOrder(orderId);
          break;
        default:
          throw new Error('Invalid action');
      }
      
      // Refresh orders list
      await fetchOrders();
      setShowModal(false);
      setSelectedOrder(null);
    } catch (error) {
      setError(`Failed to ${action} order`);
      console.error(`Error ${action} order:`, error);
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusColors = {
      PENDING: 'bg-yellow-100 text-yellow-800',
      APPROVED: 'bg-green-100 text-green-800',
      REJECTED: 'bg-red-100 text-red-800',
      DELIVERED: 'bg-blue-100 text-blue-800',
      CANCELLED: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[status] || 'bg-gray-100 text-gray-800'}`}>
        {status}
      </span>
    );
  };

  const filteredOrders = orders.filter(order => 
    filterStatus === 'all' || order.status === filterStatus
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Order Management</h1>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
        <div className="bg-white p-8 rounded-lg shadow text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Order Management</h1>
        <div className="space-x-2">
          <button
            onClick={fetchOrders}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
          >
            Refresh Orders
          </button>
          <button
            onClick={() => console.log('🔍 Admin: Current orders state:', orders)}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium"
          >
            Debug Orders
          </button>
        </div>
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Order Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-2xl">📋</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900">{orders.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <span className="text-2xl">⏳</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">
                {orders.filter(o => o.status === 'PENDING').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-2xl">✅</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-green-600">
                {orders.filter(o => o.status === 'APPROVED').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-2xl">🚚</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Delivered</p>
              <p className="text-2xl font-bold text-blue-600">
                {orders.filter(o => o.status === 'DELIVERED').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex space-x-4">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All Orders</option>
            <option value="PENDING">Pending</option>
            <option value="APPROVED">Approved</option>
            <option value="REJECTED">Rejected</option>
            <option value="DELIVERED">Delivered</option>
          </select>
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Order ID
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Gas Type & Quantity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Delivery Address
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredOrders.map((order) => (
              <tr key={order.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  #{order.id}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {order.customer_name || order.contact_phone || 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {order.gas_brand || order.brand || 'N/A'} - {order.quantity} units
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {order.delivery_address || 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  RWF {(order.total_price || order.total_amount || 0).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(order.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {new Date(order.created_at || Date.now()).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex flex-col space-y-1">
                    <button
                      onClick={() => {
                        setSelectedOrder(order);
                        setShowModal(true);
                      }}
                      className="text-blue-600 hover:text-blue-900 text-left"
                    >
                      View Details
                    </button>

                    {order.status === 'PENDING' && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleOrderAction(order.id, 'approve')}
                          className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs font-medium"
                          disabled={actionLoading}
                        >
                          {actionLoading ? 'Processing...' : 'Approve'}
                        </button>
                        <button
                          onClick={() => handleOrderAction(order.id, 'reject')}
                          className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium"
                          disabled={actionLoading}
                      >
                          {actionLoading ? 'Processing...' : 'Reject'}
                        </button>
                      </div>
                    )}

                    {order.status === 'APPROVED' && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleOrderAction(order.id, 'delivered')}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium"
                          disabled={actionLoading}
                        >
                          {actionLoading ? 'Processing...' : 'Mark Delivered'}
                        </button>
                        <button
                          onClick={() => handleOrderAction(order.id, 'reject')}
                          className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium"
                          disabled={actionLoading}
                        >
                          {actionLoading ? 'Processing...' : 'Cancel'}
                        </button>
                      </div>
                    )}

                    {(order.status === 'REJECTED' || order.status === 'DELIVERED') && (
                      <span className="text-gray-500 text-xs">No actions available</span>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredOrders.length === 0 && (
          <div className="text-center py-12">
            <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-500 mb-4">
              {orders.length === 0
                ? "No orders have been placed yet. To test the system: 1) Login as a buyer, 2) Place a test order, 3) Come back here to approve it."
                : "No orders match the current filter criteria."
              }
            </p>
            {orders.length === 0 && (
              <div className="bg-blue-50 p-4 rounded-md mb-4 text-left">
                <h4 className="font-medium text-blue-900 mb-2">Troubleshooting:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Check if buyers can place orders successfully</li>
                  <li>• Verify the API endpoint: /api/booking/orders/</li>
                  <li>• Click "Debug Orders" to see the current state</li>
                  <li>• Check browser console for API response details</li>
                </ul>
              </div>
            )}
            <button
              onClick={fetchOrders}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
            >
              Refresh Orders
            </button>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {showModal && selectedOrder && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Order Details - #{selectedOrder.id}
              </h3>
              
              <div className="space-y-3">
                <div>
                  <span className="font-medium">Status:</span> {getStatusBadge(selectedOrder.status)}
                </div>
                <div>
                  <span className="font-medium">Customer:</span> {selectedOrder.customer_name || 'N/A'}
                </div>
                <div>
                  <span className="font-medium">Phone:</span> {selectedOrder.contact_phone || 'N/A'}
                </div>
                <div>
                  <span className="font-medium">Address:</span> {selectedOrder.delivery_address || 'N/A'}
                </div>
                <div>
                  <span className="font-medium">Quantity:</span> {selectedOrder.quantity}
                </div>
                <div>
                  <span className="font-medium">Total:</span> RWF {selectedOrder.total_price || '0.00'}
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminOrders;
