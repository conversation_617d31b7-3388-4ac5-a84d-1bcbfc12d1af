import { Navigate } from 'react-router-dom';
import { useMemo } from 'react';
import { authService } from '../services/auth';

const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const isAuthenticated = useMemo(() => authService.isAuthenticated(), []);
  const userRole = useMemo(() => authService.getUserRole(), []);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles.length > 0 && !allowedRoles.includes(userRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return children;
};

export default ProtectedRoute;
