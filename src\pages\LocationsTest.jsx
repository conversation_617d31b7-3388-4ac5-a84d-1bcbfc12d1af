import { useState, useEffect } from 'react';
import { locationService, adminService } from '../services/endpoints';

const LocationsTest = () => {
  const [locations, setLocations] = useState([]);
  const [adminLocations, setAdminLocations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [logs, setLogs] = useState([]);

  const addLog = (message) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    console.log(message);
  };

  const testLocationService = async () => {
    setLoading(true);
    setError('');
    setLogs([]);
    
    try {
      addLog('🔍 Testing locationService.getLocations()...');
      const response = await locationService.getLocations();
      addLog(`✅ Response received: ${JSON.stringify(response)}`);
      addLog(`📊 Response type: ${typeof response}`);
      addLog(`🔑 Response keys: ${Object.keys(response || {}).join(', ')}`);
      
      if (Array.isArray(response)) {
        setLocations(response);
        addLog(`📋 Set ${response.length} locations from direct array`);
      } else if (response?.data && Array.isArray(response.data)) {
        setLocations(response.data);
        addLog(`📋 Set ${response.data.length} locations from response.data`);
      } else {
        addLog('❌ No array found in response');
        setLocations([]);
      }
    } catch (error) {
      addLog(`❌ Error: ${error.message}`);
      addLog(`📊 Error status: ${error.response?.status}`);
      addLog(`📊 Error data: ${JSON.stringify(error.response?.data)}`);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const testAdminService = async () => {
    setLoading(true);
    setError('');
    setLogs([]);
    
    try {
      addLog('🔍 Testing adminService.getLocations()...');
      const response = await adminService.getLocations();
      addLog(`✅ Response received: ${JSON.stringify(response)}`);
      addLog(`📊 Response type: ${typeof response}`);
      addLog(`🔑 Response keys: ${Object.keys(response || {}).join(', ')}`);
      
      if (Array.isArray(response)) {
        setAdminLocations(response);
        addLog(`📋 Set ${response.length} admin locations from direct array`);
      } else if (response?.data && Array.isArray(response.data)) {
        setAdminLocations(response.data);
        addLog(`📋 Set ${response.data.length} admin locations from response.data`);
      } else {
        addLog('❌ No array found in response');
        setAdminLocations([]);
      }
    } catch (error) {
      addLog(`❌ Error: ${error.message}`);
      addLog(`📊 Error status: ${error.response?.status}`);
      addLog(`📊 Error data: ${JSON.stringify(error.response?.data)}`);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Locations API Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Test Buttons */}
        <div className="space-y-4">
          <button
            onClick={testLocationService}
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md disabled:opacity-50"
          >
            Test locationService.getLocations()
          </button>
          
          <button
            onClick={testAdminService}
            disabled={loading}
            className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md disabled:opacity-50"
          >
            Test adminService.getLocations()
          </button>
        </div>

        {/* Logs */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Logs</h3>
          <div className="bg-gray-100 p-4 rounded-md h-64 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="text-sm mb-1 font-mono">
                {log}
              </div>
            ))}
            {logs.length === 0 && (
              <div className="text-gray-500 text-sm">Click a test button to see logs...</div>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Results */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Location Service Results */}
        <div>
          <h3 className="text-lg font-semibold mb-2">locationService Results ({locations.length})</h3>
          <div className="bg-white border rounded-md p-4 max-h-64 overflow-y-auto">
            {locations.length > 0 ? (
              locations.map((location, index) => (
                <div key={index} className="mb-2 p-2 bg-gray-50 rounded">
                  <div className="font-medium">{location.name || 'No name'}</div>
                  <div className="text-sm text-gray-600">
                    {location.sector}, {location.district}, {location.city}
                  </div>
                  <div className="text-xs text-gray-500">ID: {location.id}</div>
                </div>
              ))
            ) : (
              <div className="text-gray-500">No locations found</div>
            )}
          </div>
        </div>

        {/* Admin Service Results */}
        <div>
          <h3 className="text-lg font-semibold mb-2">adminService Results ({adminLocations.length})</h3>
          <div className="bg-white border rounded-md p-4 max-h-64 overflow-y-auto">
            {adminLocations.length > 0 ? (
              adminLocations.map((location, index) => (
                <div key={index} className="mb-2 p-2 bg-gray-50 rounded">
                  <div className="font-medium">{location.name || 'No name'}</div>
                  <div className="text-sm text-gray-600">
                    {location.sector}, {location.district}, {location.city}
                  </div>
                  <div className="text-xs text-gray-500">ID: {location.id}</div>
                </div>
              ))
            ) : (
              <div className="text-gray-500">No locations found</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationsTest;
