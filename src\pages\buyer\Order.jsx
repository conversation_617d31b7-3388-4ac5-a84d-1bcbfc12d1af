import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { buyerService, sellerService, locationService } from '../../services/endpoints';
import { GAS_TYPES } from '../../utils/constants';
import ProductCard from '../../components/ProductCard';

const Order = () => {
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedLocation, setSelectedLocation] = useState('');
  const [locations, setLocations] = useState([]);
  const [useManualLocation, setUseManualLocation] = useState(false);
  const [manualLocation, setManualLocation] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchProducts();
    fetchLocations();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🛒 Buyer: Starting to fetch products...');
      console.log('🛒 Buyer: Trying buyerService.getProducts()...');
      const response = await buyerService.getProducts();
      console.log('🛒 Buyer: Got response from buyerService:', response);
      console.log('🛒 Buyer: Response type:', typeof response);
      console.log('🛒 Buyer: Response keys:', Object.keys(response || {}));

      // Handle different response structures - the API returns nested data
      let productsData = [];

      if (Array.isArray(response)) {
        // Direct array response
        productsData = response;
        console.log('🛒 Buyer: Using direct array response');
      } else if (response?.data?.data && Array.isArray(response.data.data)) {
        // Nested data structure: response.data.data (this is our case!)
        productsData = response.data.data;
        console.log('🛒 Buyer: Using response.data.data array (nested structure)');
      } else if (response?.data && Array.isArray(response.data)) {
        // Response with data property containing array
        productsData = response.data;
        console.log('🛒 Buyer: Using response.data array');
      } else if (response?.results && Array.isArray(response.results)) {
        // Paginated response with results array
        productsData = response.results;
        console.log('🛒 Buyer: Using response.results array');
      } else if (response?.products && Array.isArray(response.products)) {
        // Response with products property
        productsData = response.products;
        console.log('🛒 Buyer: Using response.products array');
      } else if (typeof response === 'object' && response !== null) {
        // If it's an object, try to extract array from any property
        const possibleArrays = Object.values(response).filter(Array.isArray);
        if (possibleArrays.length > 0) {
          productsData = possibleArrays[0];
          console.log('🛒 Buyer: Found array in object properties');
        } else {
          console.log('🛒 Buyer: No array found in response object, trying seller service...');

          // Try seller service as fallback
          const sellerResponse = await sellerService.getStock();
          console.log('🛒 Buyer: Seller service response:', sellerResponse);

          // Handle seller service nested structure too
          if (Array.isArray(sellerResponse)) {
            productsData = sellerResponse;
          } else if (sellerResponse?.data?.data && Array.isArray(sellerResponse.data.data)) {
            productsData = sellerResponse.data.data;
            console.log('🛒 Buyer: Using seller response.data.data array');
          } else if (sellerResponse?.data && Array.isArray(sellerResponse.data)) {
            productsData = sellerResponse.data;
          } else if (sellerResponse?.results && Array.isArray(sellerResponse.results)) {
            productsData = sellerResponse.results;
          }
        }
      }

      console.log('🛒 Buyer: Final products data:', productsData);
      console.log('🛒 Buyer: Is array?', Array.isArray(productsData));
      console.log('🛒 Buyer: Length:', productsData?.length);

      setProducts(Array.isArray(productsData) ? productsData : []);
      console.log('🛒 Buyer: Set products state with', productsData?.length, 'items');

    } catch (error) {
      console.error('🛒 Buyer: Error fetching products:', error);
      console.error('🛒 Buyer: Error response:', error.response);
      console.error('🛒 Buyer: Error status:', error.response?.status);
      console.error('🛒 Buyer: Error data:', error.response?.data);
      setError('Failed to fetch products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchLocations = async () => {
    try {
      console.log('🛒 Buyer: Fetching locations...');
      const response = await locationService.getLocations();
      console.log('🛒 Buyer: Locations response:', response);

      // Handle different response structures
      let locationsData = [];
      if (Array.isArray(response)) {
        locationsData = response;
      } else if (response?.data && Array.isArray(response.data)) {
        locationsData = response.data;
      } else if (response?.results && Array.isArray(response.results)) {
        locationsData = response.results;
      }

      console.log('🛒 Buyer: Final locations data:', locationsData);
      setLocations(Array.isArray(locationsData) ? locationsData : []);
    } catch (error) {
      console.error('🛒 Buyer: Error fetching locations:', error);
      // Don't set error for locations as it's not critical for the page to load
    }
  };

  const handleProductSelect = (product) => {
    setSelectedProduct(product);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!selectedProduct) {
      setError('Please select a product');
      return;
    }

    if (!useManualLocation && !selectedLocation) {
      setError('Please select a delivery location');
      return;
    }

    if (useManualLocation && !manualLocation.trim()) {
      setError('Please enter your delivery location');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      // Determine the delivery address based on selection method
      let deliveryAddress;
      if (useManualLocation) {
        deliveryAddress = manualLocation.trim();
      } else {
        const locationObj = locations.find(loc => loc.id.toString() === selectedLocation);
        deliveryAddress = locationObj
          ? `${locationObj.name}, ${locationObj.sector}, ${locationObj.district}, ${locationObj.city}`
          : selectedLocation;
      }

      const orderData = {
        gas_inventory: selectedProduct.id,
        quantity: quantity,
        delivery_address: deliveryAddress,
        contact_phone: '0788000000', // This should come from user profile
        total_price: (selectedProduct.unit_price || selectedProduct.price) * quantity
      };

      await buyerService.createOrder(orderData);
      navigate('/buyer/orders', { 
        state: { message: 'Order placed successfully!' }
      });
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to place order');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Order Gas</h1>
        <div className="flex space-x-3">
          <button
            onClick={fetchProducts}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium"
          >
            Refresh Products
          </button>
          <span className="text-sm text-gray-500 self-center">
            Products: {products.length}
          </span>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <div className="flex justify-between items-center">
            <span>{error}</span>
            <button
              onClick={fetchProducts}
              className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Product Selection */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Select Gas Product
          </h2>
          
          {products.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-6xl mb-4">📦</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Available</h3>
              <p className="text-gray-500 mb-4">
                No gas products are currently available for order.
              </p>
              <button
                onClick={fetchProducts}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
              >
                Refresh Products
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  isSelected={selectedProduct?.id === product.id}
                  onSelect={() => handleProductSelect(product)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Order Form */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Order Details
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {selectedProduct && (
              <div className="bg-gray-50 p-4 rounded-md">
                <h3 className="font-medium text-gray-900">Selected Product</h3>
                <p className="text-sm text-gray-600">{selectedProduct.brand} - {selectedProduct.weight_kg}kg</p>
                <p className="text-sm text-gray-600">Price: RWF {parseFloat(selectedProduct.unit_price || 0).toLocaleString()}</p>
                <p className="text-sm text-gray-600">Available: {selectedProduct.quantity || 0}</p>
              </div>
            )}
            
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                Quantity
              </label>
              <input
                type="number"
                id="quantity"
                min="1"
                max={selectedProduct?.quantity || 1}
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Delivery Location
              </label>

              {/* Location method selection */}
              <div className="mb-3">
                <label className="inline-flex items-center mr-6">
                  <input
                    type="radio"
                    name="locationMethod"
                    checked={!useManualLocation}
                    onChange={() => {
                      setUseManualLocation(false);
                      setManualLocation('');
                    }}
                    className="form-radio h-4 w-4 text-blue-600"
                  />
                  <span className="ml-2 text-sm text-gray-700">Choose from list</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="locationMethod"
                    checked={useManualLocation}
                    onChange={() => {
                      setUseManualLocation(true);
                      setSelectedLocation('');
                    }}
                    className="form-radio h-4 w-4 text-blue-600"
                  />
                  <span className="ml-2 text-sm text-gray-700">Enter manually</span>
                </label>
              </div>

              {/* Location dropdown */}
              {!useManualLocation && (
                <div>
                  <select
                    id="location"
                    value={selectedLocation}
                    onChange={(e) => setSelectedLocation(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required={!useManualLocation}
                  >
                    <option value="">Select a delivery location</option>
                    {locations.map((location) => (
                      <option key={location.id} value={location.id}>
                        {location.name} - {location.sector}, {location.district}, {location.city}
                      </option>
                    ))}
                  </select>
                  {locations.length === 0 && (
                    <p className="mt-1 text-sm text-gray-500">
                      Loading locations...
                    </p>
                  )}
                </div>
              )}

              {/* Manual location input */}
              {useManualLocation && (
                <div>
                  <input
                    type="text"
                    id="manualLocation"
                    value={manualLocation}
                    onChange={(e) => setManualLocation(e.target.value)}
                    placeholder="Enter your delivery address (e.g., Kigali, Gasabo, Kimisagara)"
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required={useManualLocation}
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Please provide a detailed address including district and sector
                  </p>
                </div>
              )}
            </div>
            
            {selectedProduct && (
              <div className="bg-blue-50 p-4 rounded-md">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-900">Total Amount:</span>
                  <span className="text-xl font-bold text-blue-600">
                    RWF {((selectedProduct.unit_price || 0) * quantity).toLocaleString()}
                  </span>
                </div>
              </div>
            )}
            
            <button
              type="submit"
              disabled={!selectedProduct || !selectedLocation || submitting}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium"
            >
              {submitting ? 'Placing Order...' : 'Place Order'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Order;
