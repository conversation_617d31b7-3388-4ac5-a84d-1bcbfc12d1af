import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { buyerService, sellerService } from '../../services/endpoints';
import { GAS_TYPES } from '../../utils/constants';
import ProductCard from '../../components/ProductCard';

const Order = () => {
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [deliveryLocation, setDeliveryLocation] = useState('');
  const [loading, setLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async (isRefresh = false) => {
    try {
      // Only show full page loading on initial load, not on refresh
      if (!isRefresh) {
        setLoading(true);
      } else {
        setProductsLoading(true);
      }
      setError('');

      const response = await buyerService.getProducts();

      // Handle different response structures efficiently
      let productsData = [];

      if (Array.isArray(response)) {
        productsData = response;
      } else if (response?.data?.data && Array.isArray(response.data.data)) {
        productsData = response.data.data;
      } else if (response?.data && Array.isArray(response.data)) {
        productsData = response.data;
      } else if (response?.results && Array.isArray(response.results)) {
        productsData = response.results;
      } else if (response?.products && Array.isArray(response.products)) {
        productsData = response.products;
      } else if (typeof response === 'object' && response !== null) {
        const possibleArrays = Object.values(response).filter(Array.isArray);
        if (possibleArrays.length > 0) {
          productsData = possibleArrays[0];
        } else {
          // Try seller service as fallback
          const sellerResponse = await sellerService.getStock();

          if (Array.isArray(sellerResponse)) {
            productsData = sellerResponse;
          } else if (sellerResponse?.data?.data && Array.isArray(sellerResponse.data.data)) {
            productsData = sellerResponse.data.data;
          } else if (sellerResponse?.data && Array.isArray(sellerResponse.data)) {
            productsData = sellerResponse.data;
          } else if (sellerResponse?.results && Array.isArray(sellerResponse.results)) {
            productsData = sellerResponse.results;
          }
        }
      }

      setProducts(Array.isArray(productsData) ? productsData : []);

    } catch (error) {
      console.error('Error fetching products:', error);
      setError('Failed to fetch products. Please try again.');
    } finally {
      setLoading(false);
      setProductsLoading(false);
    }
  };



  const handleProductSelect = (product) => {
    setSelectedProduct(product);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!selectedProduct) {
      setError('Please select a product');
      return;
    }

    if (!deliveryLocation.trim()) {
      setError('Please enter your delivery location');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const deliveryAddress = deliveryLocation.trim();

      const orderData = {
        gas_inventory: selectedProduct.id,
        quantity: quantity,
        delivery_address: deliveryAddress,
        contact_phone: '0788000000', // This should come from user profile
        total_price: (selectedProduct.unit_price || selectedProduct.price) * quantity
      };

      await buyerService.createOrder(orderData);

      // Show success popup
      setShowSuccessModal(true);

      // Redirect to dashboard after 2 seconds
      setTimeout(() => {
        navigate('/buyer/dashboard');
      }, 2000);
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to place order');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Order Gas</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => fetchProducts(true)}
            disabled={productsLoading}
            className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium"
          >
            {productsLoading ? 'Refreshing...' : 'Refresh Products'}
          </button>
          <span className="text-sm text-gray-500 self-center">
            Products: {products.length}
          </span>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <div className="flex justify-between items-center">
            <span>{error}</span>
            <button
              onClick={() => fetchProducts(true)}
              disabled={productsLoading}
              className="bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-3 py-1 rounded text-sm"
            >
              {productsLoading ? 'Retrying...' : 'Retry'}
            </button>
          </div>
        </div>
      )}

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Product Selection */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Select Gas Product
          </h2>
          
          {products.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-6xl mb-4">📦</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Available</h3>
              <p className="text-gray-500 mb-4">
                No gas products are currently available for order.
              </p>
              <button
                onClick={fetchProducts}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
              >
                Refresh Products
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  isSelected={selectedProduct?.id === product.id}
                  onSelect={() => handleProductSelect(product)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Order Form */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Order Details
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {selectedProduct && (
              <div className="bg-gray-50 p-4 rounded-md">
                <h3 className="font-medium text-gray-900">Selected Product</h3>
                <p className="text-sm text-gray-600">{selectedProduct.brand} - {selectedProduct.weight_kg}kg</p>
                <p className="text-sm text-gray-600">Price: RWF {parseFloat(selectedProduct.unit_price || 0).toLocaleString()}</p>
                <p className="text-sm text-gray-600">Available: {selectedProduct.quantity || 0}</p>
              </div>
            )}
            
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                Quantity
              </label>
              <input
                type="number"
                id="quantity"
                min="1"
                max={selectedProduct?.quantity || 1}
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                required
              />
            </div>
            
            <div>
              <label htmlFor="deliveryLocation" className="block text-sm font-medium text-gray-700">
                Delivery Location
              </label>
              <input
                type="text"
                id="deliveryLocation"
                value={deliveryLocation}
                onChange={(e) => setDeliveryLocation(e.target.value)}
                placeholder="Enter your delivery address (e.g., Kigali, Gasabo, Kimisagara)"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                required
              />
              <p className="mt-1 text-sm text-gray-500">
                Please provide a detailed address including district and sector
              </p>
            </div>
            
            {selectedProduct && (
              <div className="bg-blue-50 p-4 rounded-md">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-900">Total Amount:</span>
                  <span className="text-xl font-bold text-blue-600">
                    RWF {((selectedProduct.unit_price || 0) * quantity).toLocaleString()}
                  </span>
                </div>
              </div>
            )}
            
            <button
              type="submit"
              disabled={!selectedProduct || !deliveryLocation.trim() || submitting}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium"
            >
              {submitting ? 'Placing Order...' : 'Place Order'}
            </button>
          </form>
        </div>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl p-8 m-4 max-w-md w-full">
            <div className="text-center">
              {/* Success Icon */}
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>

              {/* Success Message */}
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Order Received Successfully!
              </h3>
              <p className="text-sm text-gray-500 mb-6">
                Your gas order has been received and is being processed. You will be redirected to your dashboard shortly.
              </p>

              {/* Loading indicator */}
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                <span className="ml-2 text-sm text-gray-600">Redirecting...</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Order;
