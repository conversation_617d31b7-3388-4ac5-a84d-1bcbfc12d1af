import { useState } from 'react';
import { locationService, adminService } from '../services/endpoints';

const CreateLocationTest = () => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [formData, setFormData] = useState({
    name: 'Kimisagara',
    district: 'Gasabo',
    city: 'Kigali',
    sector: 'Kimisagara'
  });

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const createLocationWithLocationService = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      console.log('Creating location with locationService:', formData);
      const response = await locationService.createLocation(formData);
      console.log('Location created:', response);
      setMessage(`✅ Location created successfully with locationService: ${JSON.stringify(response)}`);
    } catch (error) {
      console.error('Error creating location:', error);
      setMessage(`❌ Error with locationService: ${error.message} - ${JSON.stringify(error.response?.data)}`);
    } finally {
      setLoading(false);
    }
  };

  const createLocationWithAdminService = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      console.log('Creating location with adminService:', formData);
      const response = await adminService.createLocation(formData);
      console.log('Location created:', response);
      setMessage(`✅ Location created successfully with adminService: ${JSON.stringify(response)}`);
    } catch (error) {
      console.error('Error creating location:', error);
      setMessage(`❌ Error with adminService: ${error.message} - ${JSON.stringify(error.response?.data)}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Create Location Test</h1>
      
      <div className="bg-white border rounded-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Location Data</h2>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">District</label>
            <input
              type="text"
              name="district"
              value={formData.district}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
            <input
              type="text"
              name="city"
              value={formData.city}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sector</label>
            <input
              type="text"
              name="sector"
              value={formData.sector}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <button
          onClick={createLocationWithLocationService}
          disabled={loading}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md disabled:opacity-50"
        >
          Create with locationService
        </button>
        
        <button
          onClick={createLocationWithAdminService}
          disabled={loading}
          className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md disabled:opacity-50"
        >
          Create with adminService
        </button>
      </div>

      {message && (
        <div className="mt-6 p-4 bg-gray-100 rounded-md">
          <pre className="text-sm whitespace-pre-wrap">{message}</pre>
        </div>
      )}
    </div>
  );
};

export default CreateLocationTest;
